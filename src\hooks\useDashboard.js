import { useState, useEffect, useCallback, useRef } from 'react';
import apiService from '../services/apiService';

/**
 * Custom hook for managing dashboard data
 * Handles fetching overview stats and results data
 */
export const useDashboard = (currentPage = 1, limit = 10) => {
  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  const [data, setData] = useState({
    results: [], // Keep for backward compatibility, but will contain jobs data
    jobs: [], // New jobs data
    overview: {
      summary: {
        total_assessments: 0,
        this_month: 0,
        success_rate: 0
      },
      recent_results: [],
      archetype_summary: {
        most_common: '',
        frequency: 0,
        last_archetype: '',
        unique_archetypes: 0,
        archetype_trend: ''
      }
    },
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const fetchDashboardData = useCallback(async () => {
    // Prevent API calls if component is unmounted (helps with StrictMode double calls)
    if (!isMountedRef.current) return;

    try {
      setLoading(true);
      setError('');

      // Fetch overview and jobs data in parallel
      const [overviewResponse, jobsResponse] = await Promise.all([
        apiService.getStatsOverview(),
        apiService.getUserJobs({ page: currentPage, limit, sort: 'created_at', order: 'DESC' })
      ]);

      // Check again if component is still mounted before updating state
      if (!isMountedRef.current) return;

      const newData = { ...data };

      if (overviewResponse.success) {
        newData.overview = overviewResponse.data;
      }

      if (jobsResponse.success) {
        newData.jobs = jobsResponse.data.jobs || [];
        // Keep results for backward compatibility, but populate with jobs data
        newData.results = jobsResponse.data.jobs || [];

        // Calculate pagination from jobs response
        const total = jobsResponse.data.total || 0;
        const totalPages = Math.ceil(total / limit);

        newData.pagination = {
          page: currentPage,
          limit,
          total,
          totalPages
        };
      }

      setData(newData);

    } catch (err) {
      // Only update error state if component is still mounted
      if (isMountedRef.current) {
        setError(err.response?.data?.message || 'Failed to load dashboard data');
      }
    } finally {
      // Only update loading state if component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [currentPage, limit]);

  const deleteResult = useCallback(async (jobIdOrResultId) => {
    try {
      // For now, we'll use the existing deleteResult API
      // In the future, this might need to be updated to handle job deletion specifically
      const response = await apiService.deleteResult(jobIdOrResultId);
      if (response.success) {
        // Remove from local state (both results and jobs arrays)
        setData(prevData => ({
          ...prevData,
          results: prevData.results.filter(item =>
            item.id !== jobIdOrResultId &&
            item.job_id !== jobIdOrResultId &&
            item.result_id !== jobIdOrResultId
          ),
          jobs: prevData.jobs.filter(job =>
            job.job_id !== jobIdOrResultId &&
            job.result_id !== jobIdOrResultId
          )
        }));

        // Refresh data to get updated stats
        await fetchDashboardData();
        return { success: true };
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to delete item';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  }, [fetchDashboardData]);

  const refreshData = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  useEffect(() => {
    fetchDashboardData();

    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchDashboardData]);

  return {
    data,
    loading,
    error,
    deleteResult,
    refreshData,
    setError
  };
};

export default useDashboard;
